import { Context } from '../../../types';
import { queries, responses } from '../../graphQL';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import {
  ActionError,
  GraphQLError,
  ActionValidationError,
} from '@common/errors';
import { UpdateEventPayloadResponse } from '../../../../../../types/responses';

const createStructuredData = async <
  ReqPayload,
  Data extends Partial<
    UpdateEventPayloadResponse &
      responses.getEvent & { eventSchemaIds?: string[] }
  > = object,
>(
  context: Context<ReqPayload, Data>
): Promise<
  | Context<
      ReqPayload,
      Data &
        Partial<
          UpdateEventPayloadResponse &
            responses.createStructuredData &
            responses.getEvent
        >
    >
  | undefined
> => {
  const { cache, data, req, log, redisWrapper } = context;
  const headers = { Authorization: req.headers.authorization };

  const {
    event,
    folder,
    name,
    description,
    tags,
    eventStartDate,
    eventEndDate,
    currentTime,
    eventSchemaIds,
  } = data;

  if (!folder || !event) {
    throw new ActionError('Missing folder and event data');
  }

  const schemaId = cache.get('eventsSchemaId');
  if (!schemaId) {
    throw new ActionValidationError('schemaId not found');
  }

  try {
    if (!eventSchemaIds || eventSchemaIds.length === 0) {
      throw new ActionError('No event schema IDs found');
    }

    let event = data.event;

    if (!event) {
      const eventContentTemplate = folder?.contentTemplates.find((template) =>
        eventSchemaIds.includes(template.sdo.schemaId)
      );
      const eventId = eventContentTemplate?.sdo.id;

      if (!eventId) {
        throw new ActionError('No event found for the provided schema IDs');
      }

      event = eventContentTemplate.sdo.data;
    }

    const { createStructuredData } = await callGQL<
      responses.createStructuredData,
      ReqPayload,
      Data
    >(context, headers, queries.createStructuredData, {
      id: event.id,
      schemaId,
      data: {
        ...event,
        ...(name && { name }),
        ...((description || description === '') && { description }),
        ...(tags && { tags }),
        ...(eventStartDate && { eventStartDate }),
        ...(eventEndDate && { eventEndDate }),
        modifiedDateTime: currentTime,
      },
    });

    if (createStructuredData) {
      if (redisWrapper && data.folder) {
        const orgId = data.folder.parent.organization.id;
        await redisWrapper.event.del(
          createStructuredData.data?.id ?? event.id,
          orgId
        );
      }

      const new_data = Object.assign({}, data, {
        createStructuredData,
      });
      const new_context = Object.assign({}, context, { data: new_data });
      return new_context;
    }
  } catch (e) {
    log.error(e);
    throw new GraphQLError(e);
  }
};

export default createStructuredData;
