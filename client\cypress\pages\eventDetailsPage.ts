import { DataTable } from '@badeball/cypress-cucumber-preprocessor';
import { EventDetailDataTestId } from '../support/helperFunction/evemtDetailHelper';

export const eventDetailsPage = {
  verifyEventDetailPanel: (eventName: string) => {
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.HOME_DETAIL_NAME }).should('be.visible').and('have.text', eventName);
  },

  verifyHeaderAppName: (appName: string) => {
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.APPBAR_TITLE }).should('contain.text', appName);
  },

  verifyBreadcrumb: (eventName: string) => {
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.EVENT_BREADCRUMB }).should('contain.text', eventName);
  },

  verifyEventTitle: (eventTitle: string) => {
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.EVENT_HEADING_NAME }).should('have.text', eventTitle);
  },

  verifyButtons: (uploadButton: string, searchButton: string) => {
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.SEARCH_UPLOAD_UPLOAD_BUTTON }).should('contain.text', uploadButton);
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.SEARCH_UPLOAD_SEARCH_ALL_FILE_BUTTON }).should('contain.text', searchButton);
  },

  verifyTabs: (filesTab: string, matchTab: string, timelineTab: string) => {
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.EVENT_FILES_TAB }).should('have.text', filesTab);
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.EVENT_MATCH_GROUPS_TAB }).should('have.text', matchTab);
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.EVENT_TIMELINE_TAB }).should('have.text', timelineTab);
  },

  verifyTableLoadingSkeletonNotVisible: () => {
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.EVENT_NO_RESULTS }).should('be.visible');
  },

  clickBreadcrumb: (breadcrumbText: string) => {
    cy.get('[aria-label="breadcrumb"]').contains(breadcrumbText).click();
  },

  verifyNavigationToMainScreen: () => {
    cy.url().should('not.include', '/event');
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.UPLOAD_FILE_BTN }).should('be.visible');
  },

  clickEditEvent: () => {
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.EVENT_EDIT_EVENT }).should('be.visible').click();
  },

  enterEventName: (newEventName: string) => {
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.EVENT_CHANGE_NAME }).should('be.visible').as('eventNameInput');
    cy.get('@eventNameInput').clear();
    cy.get('@eventNameInput').type(newEventName);
  },

  enterDescription: (description: string) => {
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.EVENT_CHANGE_DESCRIPTION }).should('be.visible').clear();
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.EVENT_CHANGE_DESCRIPTION }).should('be.visible').type(description);
  },

  addTag: (tagName: string) => {
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.EVENT_TAGS_AUTOCOMPLETE }).should('be.visible').type(`${tagName}{enter}`);
  },

  setStartDate: (day: string, month: string) => {
    cy.get('input[aria-label*="Choose date"]').first().click();
    cy.get('[role="dialog"]').within(() => {
      const navigateToMonth = (targetMonth: string) => {
        cy.get('[role="presentation"][aria-live="polite"]').then(($label) => {
          if (!$label.text().includes(targetMonth)) {
            cy.get('button[title="Previous month"]').click();
            navigateToMonth(targetMonth);
          }
        });
      };
      navigateToMonth(month);
      cy.contains('button[role="gridcell"]', new RegExp(`^${day}$`)).click();
      cy.contains('button', 'OK').click();
    });
  },

  clickSaveEvent: () => {
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.EVENT_SAVE_EVENT }).should('be.visible').click();
  },

  verifySuccessMessage: (message: string) => {
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.SNACKBAR_BOX }).should('be.visible').and('contain.text', message);
  },

  verifyEventTitleUpdated: (updatedName: string) => {
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.EVENT_HEADING_NAME }).should('have.text', updatedName);
  },

  verifyEventDescriptionUpdated: (updatedDescription: string) => {
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.EVENT_DESCRIPTION }).should('have.text', updatedDescription);
  },

  verifyEventTagsUpdated: (tagName: string) => {
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.EVENT_TABLES }).contains(tagName).should('be.visible');
  },

  verifyEventDateUpdated: (expectedDateString: string) => {
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.EVENT_DATE }).should((element) => {
      expect(element.text().trim()).to.satisfy((text: string) => text.startsWith(expectedDateString));
    });
  },

  enterLongDescription: (longDescription: string) => {
    cy.wrap(longDescription).as('longDescription');
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.EVENT_CHANGE_DESCRIPTION }).should('be.visible').clear();
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.EVENT_CHANGE_DESCRIPTION }).should('be.visible').type(longDescription);
  },

  verifyLongDescription: () => {
    cy.get('@longDescription').then((longDescription) => {
      cy.getDataIdCy({ idAlias: EventDetailDataTestId.EVENT_DESCRIPTION }).should('have.text', longDescription);
    });
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.EVENT_DESCRIPTION }).then(($desc) => {
      const descriptionWidth = $desc.width() || 0;
      const parentWidth = $desc.parent().width() || 0;
      expect(descriptionWidth).to.be.at.most(parentWidth);
    });
  },

  changeResultsPerPage: (dataTable: DataTable) => {
    const values = dataTable.raw().flat();
    values.forEach((results: string) => {
      cy.getDataIdCy({ idAlias: EventDetailDataTestId.TABLE_PAGINATION_PAGE_SIZE }).click();
      cy.get(`[data-testid="table-pagination-page-size-menu-item-${results}"]`).click();
      cy.getDataIdCy({ idAlias: EventDetailDataTestId.TABLE_PAGINATION_PAGE_SIZE }).should('contain.text', results);
    });
  },

  clickPageButton: (direction: 'next' | 'previous') => {
    const selector = direction === 'next' ? 'next' : 'back';
    cy.get(`[data-testid="table-pagination-page-selector-${selector}"]`).click();
  },

  verifyPageButtonState: (direction: 'next' | 'previous', state: 'enabled' | 'disabled') => {
    const selector = direction === 'next' ? 'next' : 'back';
    const assertion = state === 'enabled' ? 'have.class' : 'not.have.class';
    cy.get(`[data-testid="table-pagination-page-selector-${selector}"]`).should(assertion, 'enabled');
  },

  clickButton: (buttonText: string) => {
    cy.contains('button', buttonText).click();
  },

  verifySearchAllFilesDialog: () => {
    cy.get('[role="dialog"]').should('be.visible');
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.SEARCH_ALL_FILES_DIALOG_TITLE }).should('be.visible');
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.SEARCH_ALL_FILES_DIALOG_ATTRIBUTE_ITEM }).should('have.length.greaterThan', 0);
  },

  verifyDialogElements: (dataTable: DataTable) => {
    const elements = dataTable.rows().flat();
    elements.forEach((elementDescription: string) => {
      cy.get('[role="dialog"]').within(() => {
        switch (elementDescription) {
        case 'People radio button':
          cy.contains('label', 'People').should('be.visible');
          break;
        case 'Vehicles radio button':
          cy.contains('label', 'Vehicles').should('be.visible');
          break;
        case 'Clear All button':
          cy.getDataIdCy({ idAlias: EventDetailDataTestId.SEARCH_ALL_FILES_DIALOG_CLEAR_ALL }).should('be.visible');
          break;
        case 'New Match Group button':
          cy.getDataIdCy({ idAlias: EventDetailDataTestId.SEARCH_ALL_FILES_DIALOG_NEW_MATCH_GROUP }).should('be.visible');
          break;
        case 'Match Groups select':
          cy.getDataIdCy({ idAlias: EventDetailDataTestId.SEARCH_ALL_FILES_DIALOG_MATCH_GROUP_SELECT }).should('be.visible');
          break;
        case 'Cancel button':
          cy.getDataIdCy({ idAlias: EventDetailDataTestId.SEARCH_ALL_FILES_DIALOG_CANCEL }).should('be.visible');
          break;
        case 'Search People button':
          cy.getDataIdCy({ idAlias: EventDetailDataTestId.SEARCH_ALL_FILES_DIALOG_SEARCH }).should('be.visible').and('contain.text', 'Search People');
          break;
        default:
          throw new Error(`Element "${elementDescription}" not defined in step.`);
        }
      });
    });
  },

  selectSearchDialogOption: (option: string) => {
    cy.get('[role="dialog"]').within(() => {
      cy.contains('label', option).click();
    });
  },

  verifyDialogAttributes: (type: string, dataTable: DataTable) => {
    cy.log(`--- Verifying attributes for ${type} ---`);
    const expectedAttributes = dataTable.raw().flat();

    cy.get('[role="dialog"]').within(() => {
      cy.getDataIdCy({ idAlias: EventDetailDataTestId.SEARCH_ALL_FILES_DIALOG_ATTRIBUTE_ITEM }).should('have.length', expectedAttributes.length);

      expectedAttributes.forEach((attribute) => {
        cy.getDataIdCy({ idAlias: EventDetailDataTestId.SEARCH_ALL_FILES_DIALOG_ATTRIBUTE_ITEM }).contains(attribute).scrollIntoView();
        cy.getDataIdCy({ idAlias: EventDetailDataTestId.SEARCH_ALL_FILES_DIALOG_ATTRIBUTE_ITEM }).contains(attribute).should('be.visible');
      });
    });
  },

  verifyAttributeSelectedCount: (count: number) => {
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.SEARCH_ALL_FILES_COUNT }).should('contain.text', `${count.toString()} Selected`);
  },

  selectAttributeCategory: (category: string) => {
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.SEARCH_ALL_FILES_DIALOG_ATTRIBUTE_ITEM }).contains(category).click();
  },

  toggleAttributeCheckbox: (action: 'checks' | 'un-checks', attribute: string) => {
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.SEARCH_ALL_CHECKBOX_ITEM })
      .contains(attribute)
      .find('input[type="checkbox"]')
      .then(($checkbox) => {
        if (action === 'checks') {
          cy.wrap($checkbox).check();
        } else {
          cy.wrap($checkbox).uncheck();
        }
      });
  },

  verifyNotificationMessage: (message: string) => {
    cy.contains('[data-testid^="snackbar-box-"]', message).should('be.visible');
  },

  clickButtonInSearchDialog: (buttonText: string) => {
    cy.get('[role="dialog"]').contains('button', buttonText).click();
  },

  checkAttributes: (dataTable: DataTable) => {
    const attributes = dataTable.rows().flat();
    attributes.forEach((attribute: string) => {
      cy.getDataIdCy({ idAlias: EventDetailDataTestId.SEARCH_ALL_CHECKBOX_ITEM }).contains(attribute).find('input[type="checkbox"]').check();
    });
  },

  createNewMatchGroup: (matchGroupName: string) => {
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.SEARCH_ALL_NEW_MATCH_INPUT }).find('input').type(matchGroupName);
    cy.getDataIdCy({ idAlias: EventDetailDataTestId.SEARCH_ALL_NEW_MATCH_CONFIRM }).click();
  },

  clickTab: (tabName: string) => {
    cy.contains('[role="tab"]', tabName).click();
  },

  verifyMatchGroupInList: (matchGroupName: string, searchCount: string) => {
    cy.contains('[data-testid^="match-group-row-"]', matchGroupName).find('span').should('have.text', searchCount);
  },

  deleteMatchGroup: (matchGroupName: string) => {
    cy.contains('[data-testid^="match-group-row-"]', matchGroupName).find('[data-testid^="match-group-row-menu-icon-"]').click();
    cy.get('[data-testid^="match-group-row-menu-delete-"]').click();
  },

  clickButtonInConfirmationDialog: (buttonText: string) => {
    cy.get('[role="dialog"]').contains('button', buttonText).click();
  },

  verifyMatchGroupNotInList: (matchGroupName:string) => {
    cy.contains('[data-testid^="match-group-row-"]', matchGroupName).should('not.exist');
  },

  verifySearchDialogClosed: () => {
    cy.get('[role="dialog"]').should('not.exist');
  }
};
