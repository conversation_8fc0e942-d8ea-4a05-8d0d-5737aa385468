import {
  MatchGroupDataTestId,
  MatchGroupTab,
  MatchGroupButton,
  MatchGroupText,
} from '../support/helperFunction/matchGroupHelper';

export const matchGroupPage = {
  verifyMatchGroupPageUI: () => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.MATCH_GROUP }).should('be.visible');
  },

  verifyBreadcrumb: (eventName: string, groupName: string) => {
    cy.get('[aria-label="breadcrumb"]').should('be.visible');
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.EVENT_BREADCRUMB }).should('contain.text', eventName);
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.MATCH_GROUP_BREADCRUMB }).should('contain.text', groupName);
    cy.contains(MatchGroupText.ALL_EVENTS).should('be.visible');
  },

  verifyFileNameDropdownAndExportButton: () => {
    cy.get('select, [role="combobox"], .MuiSelect-root').should('exist');
    cy.contains('button', MatchGroupButton.EXPORT).should('be.visible');
  },

  verifyLeftScreenContent: () => {
    cy.contains(MatchGroupText.SELECT_TRACKLET).should('be.visible');
  },

  verifyRightScreenTabs: () => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.MATCH_GROUP_TABS }).should('be.visible');
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.MATCH_GROUP_TAB_SEARCH }).should('contain.text', MatchGroupTab.SEARCH_RESULTS);
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.MATCH_GROUP_TAB_DETAIL }).should('contain.text', MatchGroupTab.VERIFIED_MATCHES);
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.MATCH_GROUP_TAB_TIMELINE }).should('contain.text', MatchGroupTab.TIMELINE_EDITOR);
  },

  verifyDefaultTab: () => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.MATCH_GROUP_TAB_DETAIL }).should('have.attr', 'aria-selected', 'true');
  },

  hoverOnMatchGroupSearch: (searchName: string) => {
    cy.contains(`[data-testid^="${MatchGroupDataTestId.MATCH_GROUP_ROW}"]`, searchName).trigger('mouseover');
  },

  clickEditIcon: (searchId: string) => {
    cy.get(`[data-testid="${MatchGroupDataTestId.MATCHGROUP_EDIT}${searchId}"]`).click();
  },

  clickDeleteIcon: (searchId: string) => {
    cy.get(`[data-testid="${MatchGroupDataTestId.MATCHGROUP_DELETE}${searchId}"]`).click();
  },

  hoverOnFirstMatchGroupSearch: () => {
    cy.get(`[data-testid^="${MatchGroupDataTestId.MATCH_GROUP_ROW}"]`).first().trigger('mouseover');
  },

  clickFirstEditIcon: () => {
    cy.get(`[data-testid^="${MatchGroupDataTestId.MATCHGROUP_EDIT}"]`).first().click();
  },

  clickFirstDeleteIcon: () => {
    cy.get(`[data-testid^="${MatchGroupDataTestId.MATCHGROUP_DELETE}"]`).first().click();
  },

  enterNewSearchName: (newName: string) => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.CONFIRM_DIALOG_RENAME_INPUT }).clear();
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.CONFIRM_DIALOG_RENAME_INPUT }).type(newName);
  },

  clickSaveInDialog: () => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.CONFIRM_DIALOG_MATCH_GROUP }).contains('button', MatchGroupButton.SAVE).click();
  },

  clickDeleteInDialog: () => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.CONFIRM_DIALOG_MATCH_GROUP }).contains('button', MatchGroupButton.DELETE).click();
  },

  verifySearchNameUpdated: (newName: string) => {
    cy.contains(`[data-testid^="${MatchGroupDataTestId.MATCH_GROUP_ROW}"]`, newName).should('be.visible');
  },

  verifySearchDeleted: (searchName: string) => {
    cy.contains(`[data-testid^="${MatchGroupDataTestId.MATCH_GROUP_ROW}"]`, searchName).should('not.exist');
  },

  clickThreeDotsButton: () => {
    cy.get(`[data-testid^="${MatchGroupDataTestId.MATCH_GROUP_ROW_MENU_ICON}"]`).first().click();
  },

  selectRename: () => {
    cy.get(`[data-testid^="${MatchGroupDataTestId.MATCH_GROUP_ROW_MENU_RENAME}"]`).click();
  },

  selectDelete: () => {
    cy.get(`[data-testid^="${MatchGroupDataTestId.MATCH_GROUP_ROW_MENU_DELETE}"]`).click();
  },

  enterNewMatchGroupName: (newName: string) => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.CONFIRM_DIALOG_RENAME_INPUT }).clear();
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.CONFIRM_DIALOG_RENAME_INPUT }).type(newName);
  },

  clickYesDelete: () => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.CONFIRM_DIALOG_MATCH_GROUP }).contains('button', MatchGroupButton.YES_DELETE).click();
  },

  verifyDeleteConfirmationModal: () => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.CONFIRM_DIALOG_MATCH_GROUP }).should('be.visible');
    cy.contains(MatchGroupText.DELETE_CONFIRMATION).should('be.visible');
    cy.contains('button', MatchGroupButton.CANCEL).should('be.visible');
    cy.contains('button', MatchGroupButton.YES_DELETE).should('be.visible');
  },

  verifyMatchGroupDeleted: () => {
    cy.url().should('not.include', '/match-group/');
  },

  verifyMatchGroupNameUpdated: (newName: string) => {
    cy.getDataIdCy({ idAlias: MatchGroupDataTestId.MATCH_GROUP_BREADCRUMB }).should('contain.text', newName);
  }
};
