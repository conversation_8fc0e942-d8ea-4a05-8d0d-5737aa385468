export const matchGroupPage = {
  verifyMatchGroupPageUI: () => {
    cy.get('[data-testid="match-group"]').should('be.visible');
  },

  verifyBreadcrumb: (eventName: string, groupName: string) => {
    cy.get('[aria-label="breadcrumb"]').should('be.visible');
    cy.get('[data-testid="event-breadcrumb"]').should('contain.text', eventName);
    cy.get('[data-testid="match-group-breadcrumb"]').should('contain.text', groupName);
    cy.contains('All Events').should('be.visible');
  },

  verifyFileNameDropdownAndExportButton: () => {
    cy.get('select, [role="combobox"], .MuiSelect-root').should('exist');
    cy.contains('button', 'Export').should('be.visible');
  },

  verifyLeftScreenContent: () => {
    cy.contains('Select a Tracklet to View Details').should('be.visible');
  },

  verifyRightScreenTabs: () => {
    cy.get('[data-testid="match-group-tabs"]').should('be.visible');
    cy.get('[data-testid="match-group-tab-search"]').should('contain.text', 'Search Results');
    cy.get('[data-testid="match-group-tab-detail"]').should('contain.text', 'Verified Matches');
    cy.get('[data-testid="match-group-tab-timeline"]').should('contain.text', 'Timeline Editor');
  },

  verifyDefaultTab: () => {
    cy.get('[data-testid="match-group-tab-detail"]').should('have.attr', 'aria-selected', 'true');
  },

  hoverOnMatchGroupSearch: (searchName: string) => {
    cy.contains('[data-testid^="match-group-row-"]', searchName).trigger('mouseover');
  },

  clickEditIcon: (searchId: string) => {
    cy.get(`[data-testid="matchgroup-edit-${searchId}"]`).click();
  },

  clickDeleteIcon: (searchId: string) => {
    cy.get(`[data-testid="matchgroup-delete-${searchId}"]`).click();
  },

  hoverOnFirstMatchGroupSearch: () => {
    cy.get('[data-testid^="match-group-row-"]').first().trigger('mouseover');
  },

  clickFirstEditIcon: () => {
    cy.get('[data-testid^="matchgroup-edit-"]').first().click();
  },

  clickFirstDeleteIcon: () => {
    cy.get('[data-testid^="matchgroup-delete-"]').first().click();
  },

  enterNewSearchName: (newName: string) => {
    cy.get('[data-testid="confirm-dialog-rename-input"]').clear();
    cy.get('[data-testid="confirm-dialog-rename-input"]').type(newName);
  },

  clickSaveInDialog: () => {
    cy.get('[data-testid="confirm-dialog-matchGroup"]').contains('button', 'Save').click();
  },

  clickDeleteInDialog: () => {
    cy.get('[data-testid="confirm-dialog-matchGroup"]').contains('button', 'Delete').click();
  },

  verifySearchNameUpdated: (newName: string) => {
    cy.contains('[data-testid^="match-group-row-"]', newName).should('be.visible');
  },

  verifySearchDeleted: (searchName: string) => {
    cy.contains('[data-testid^="match-group-row-"]', searchName).should('not.exist');
  },

  clickThreeDotsButton: () => {
    cy.get('[data-testid^="match-group-row-menu-icon-"]').first().click();
  },

  selectRename: () => {
    cy.get('[data-testid^="match-group-row-menu-rename-"]').click();
  },

  selectDelete: () => {
    cy.get('[data-testid^="match-group-row-menu-delete-"]').click();
  },

  enterNewMatchGroupName: (newName: string) => {
    cy.get('[data-testid="confirm-dialog-rename-input"]').clear();
    cy.get('[data-testid="confirm-dialog-rename-input"]').type(newName);
  },

  clickYesDelete: () => {
    cy.get('[data-testid="confirm-dialog-matchGroup"]').contains('button', 'Yes, Delete').click();
  },

  verifyDeleteConfirmationModal: () => {
    cy.get('[data-testid="confirm-dialog-matchGroup"]').should('be.visible');
    cy.contains('Are you sure you want to delete this match group? This will remove all searches and timelines associated to it.').should('be.visible');
    cy.contains('button', 'Cancel').should('be.visible');
    cy.contains('button', 'Yes, Delete').should('be.visible');
  },

  verifyMatchGroupDeleted: () => {
    cy.url().should('not.include', '/match-group/');
  },

  verifyMatchGroupNameUpdated: (newName: string) => {
    cy.get('[data-testid="match-group-breadcrumb"]').should('contain.text', newName);
  }
};
