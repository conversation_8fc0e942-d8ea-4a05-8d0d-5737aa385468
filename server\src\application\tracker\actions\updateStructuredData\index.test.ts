import NodeCache from 'node-cache';
import consoleLogger from '../../../../logger';
import { Variables } from 'graphql-request';
import GQLApi from '../../../../util/api/graphQL';
import { Context, RequestHeader } from '../../../types';
import updateStructuredData from '../updateStructuredData';
import { callGQL } from '@util/api/graphQL/callGraphQL';
import { createRequest, createResponse } from 'node-mocks-http';
import { queries, responses } from '@tracker/graphQL';
import * as ResTypes from '../../../../../../types/responses';
import Redis from 'ioredis-mock';
import RedisWrapper from '../../../../redisWrapper';

let cxt: Context<
  object,
  responses.getEvent &
    ResTypes.UpdateEventPayloadResponse & { eventSchemaIds: string[] }
>;
let clientRedis: InstanceType<typeof Redis>;

jest.mock('@util/api/graphQL/callGraphQL', () => ({
  callGQL: jest.fn(
    (
      _context: Context<object, object>,
      _headers: RequestHeader,
      query: string,
      _variables?: Variables
    ) => {
      if (query.includes(queries.fetchAllSchemaIds)) {
        return Promise.resolve({
          dataRegistry: {
            schemas: {
              records: [
                {
                  id: 'schemaId',
                },
                {
                  id: 'schemaId',
                },
                {
                  id: 'id',
                },
                {
                  id: 'eventId',
                },
                {
                  id: 'an-event-id',
                },
                {
                  id: 'sdoId',
                },
              ],
            },
          },
        });
      }

      if (query.includes(queries.getFolder)) {
        return Promise.resolve({
          folder: {
            id: 'an-event-id',
            contentTemplates: [
              {
                id: 'id',
                sdo: {
                  id: 'sdoId',
                  schemaId: 'schemaId',
                  data: {
                    id: 'eventId',
                    name: 'oldName',
                    tags: ['oldTag'],
                    createdBy: 'oldCreatedBy',
                    createdByName: 'oldCreatedByName',
                    description: 'oldDescription',
                    eventStartDate: 'oldEventStartDate',
                    eventEndDate: 'oldEventEndDate',
                  },
                },
              },
            ],
            parent: {
              organization: {
                id: 'organizationId',
              },
            },
          },
        });
      }

      if (query.includes(queries.createStructuredData)) {
        return Promise.resolve({
          createStructuredData: {
            id: 'sdoId',
            schemaId: 'schemaId',
            data: {
              id: 'eventId',
              name: 'newName',
              description: 'newDescription',
              tags: ['New Tag 1', 'New Tag 2'],
              eventStartDate: 'newStartDate',
              eventEndDate: 'newEndDate',
              modifiedDateTime: 'currentTime',
            },
          },
        });
      }
    }
  ),
}));

describe('Create Structured Data', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    clientRedis = new Redis();
    cxt = {
      data: {
        eventSchemaIds: ['schemaId'],
        folder: {
          contentTemplates: [
            {
              sdo: {
                id: 'sdoId',
                schemaId: 'schemaId',
                data: {
                  id: 'eventId',
                  name: '',
                  tags: [],
                  createdBy: '',
                  createdByName: '',
                  description: '',
                  eventStartDate: '',
                  eventEndDate: '',
                  createdDateTime: '',
                  modifiedDateTime: '',
                  matchGroupsCount: 0,
                  filesCount: 0,
                },
              },
              id: '',
            },
          ],
          id: '',
          name: '',
          description: '',
          createdDateTime: '',
          modifiedDateTime: '',
          parent: {
            organization: {
              id: 'orgId',
            },
          },
        },
        event: {
          id: 'eventId',
          name: '',
          tags: [],
          createdBy: '',
          createdByName: '',
          description: '',
          eventStartDate: '',
          eventEndDate: '',
          createdDateTime: '',
          modifiedDateTime: '',
          matchGroupsCount: 0,
          filesCount: 0,
        },
        name: 'newName',
        description: 'newDescription',
        tags: ['New Tag 1', 'New Tag 2'],
        eventStartDate: 'newStartDate',
        eventEndDate: 'newEndDate',
        currentTime: 'currentTime',
      },
      req: createRequest({
        headers: {
          authorization: 'Bearer validToken',
        },
      }),
      log: consoleLogger(),
      res: createResponse(),
      cache: new NodeCache({ checkperiod: 60, deleteOnExpire: true }),
      queries: {},
      gql: new GQLApi('endpoint', 'token', 'veritoneAppId'),
      redisWrapper: new RedisWrapper(clientRedis, consoleLogger()),
    };
    cxt.cache.set('eventsSchemaId', 'schemaId');
  });

  it('Updates if an event exists in data', async () => {
    const response = await updateStructuredData(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        id: 'eventId',
        schemaId: 'schemaId',
        data: {
          ...cxt.data.event,
          name: 'newName',
          description: 'newDescription',
          tags: ['New Tag 1', 'New Tag 2'],
          eventStartDate: 'newStartDate',
          eventEndDate: 'newEndDate',
          modifiedDateTime: 'currentTime',
        },
      }
    );
    expect(response).not.toBeNull();
  });

  it('Updates if an event exists in data and description is empty', async () => {
    cxt.data.description = '';

    const response = await updateStructuredData(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        id: 'eventId',
        schemaId: 'schemaId',
        data: {
          ...cxt.data.event,
          name: 'newName',
          description: '',
          tags: ['New Tag 1', 'New Tag 2'],
          eventStartDate: 'newStartDate',
          eventEndDate: 'newEndDate',
          modifiedDateTime: 'currentTime',
        },
      }
    );
    expect(response).not.toBeNull();
  });

  it('Throws an error if there is missing required data', async () => {
    cxt.data = {} as any;

    expect(async () => await updateStructuredData(cxt)).rejects.toThrow(
      'Missing folder and event data'
    );
    expect(callGQL).not.toHaveBeenCalled();
  });

  it('Update event in redis', async () => {
    if (!cxt?.redisWrapper?.event) {
      throw new Error('Event not found');
    }
    // cxt.data.id = 'eventId';
    cxt.redisWrapper.event.set('eventId', 'orgId', {
      id: 'eventId',
      name: 'Sample Event',
      tags: ['tag1', 'tag2'],
      createdBy: 'user123',
      createdByName: 'User 123',
      description: 'Sample Description',
      eventStartDate: '2021-01-01',
      eventEndDate: '2021-01-02',
      createdDateTime: '2021-01-01',
      modifiedDateTime: '2021-01-02',
      matchGroupsCount: 1,
      filesCount: 1,
    });
    const redisData = await cxt.redisWrapper.event.get('eventId', 'orgId');
    expect(redisData).toEqual({
      id: 'eventId',
      name: 'Sample Event',
      tags: ['tag1', 'tag2'],
      createdBy: 'user123',
      createdByName: 'User 123',
      description: 'Sample Description',
      eventStartDate: '2021-01-01',
      eventEndDate: '2021-01-02',
      createdDateTime: '2021-01-01',
      modifiedDateTime: '2021-01-02',
      matchGroupsCount: 1,
      filesCount: 1,
    });

    const response = await updateStructuredData(cxt);
    expect(callGQL).toHaveBeenCalledTimes(1);
    expect(callGQL).toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      expect.anything(),
      {
        id: 'eventId',
        schemaId: 'schemaId',
        data: {
          ...cxt.data.event,
          name: 'newName',
          description: 'newDescription',
          tags: ['New Tag 1', 'New Tag 2'],
          eventStartDate: 'newStartDate',
          eventEndDate: 'newEndDate',
          modifiedDateTime: 'currentTime',
        },
      }
    );
    expect(response).not.toBeNull();
    if (!response?.redisWrapper?.event) {
      throw new Error('Event not found');
    }
    const updateRedisData = await response.redisWrapper.event.get(
      'eventId',
      'orgId'
    );
    expect(updateRedisData).toBeNull();
  });
});
