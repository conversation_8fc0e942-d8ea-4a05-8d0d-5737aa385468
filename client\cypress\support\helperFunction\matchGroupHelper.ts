export enum MatchGroupDataTestId {
  MATCH_GROUP = 'match-group',
  MATCH_GROUP_TABS = 'match-group-tabs',
  MATCH_GROUP_TAB_SEARCH = 'match-group-tab-search',
  MATCH_GROUP_TAB_DETAIL = 'match-group-tab-detail',
  MATCH_GROUP_TAB_TIMELINE = 'match-group-tab-timeline',
  MATCH_GROUP_BREADCRUMB = 'match-group-breadcrumb',
  EVENT_BREADCRUMB = 'event-breadcrumb',
  MATCH_GROUP_ROW = 'match-group-row-',
  MATCH_GROUP_ROW_MENU_ICON = 'match-group-row-menu-icon-',
  MATCH_GROUP_ROW_MENU_RENAME = 'match-group-row-menu-rename-',
  MATCH_GROUP_ROW_MENU_DELETE = 'match-group-row-menu-delete-',
  MATCHGROUP_EDIT = 'matchgroup-edit-',
  MATCHGROUP_DELETE = 'matchgroup-delete-',
  CONFIRM_DIALOG_MATCH_GROUP = 'confirm-dialog-matchGroup',
  CONFIRM_DIALOG_RENAME_INPUT = 'confirm-dialog-rename-input',
}

export enum MatchGroupTab {
  SEARCH_RESULTS = 'Search Results',
  VERIFIED_MATCHES = 'Verified Matches',
  TIMELINE_EDITOR = 'Timeline Editor',
}

export enum MatchGroupButton {
  VIEW_MATCH_GROUP = 'View Match Group',
  EXPORT = 'Export',
  SAVE = 'Save',
  DELETE = 'Delete',
  YES_DELETE = 'Yes, Delete',
  CANCEL = 'Cancel',
}

export enum MatchGroupMenu {
  RENAME = 'Rename',
  DELETE = 'Delete',
}

export enum MatchGroupText {
  SELECT_TRACKLET = 'Select a Tracklet to View Details',
  DELETE_CONFIRMATION = 'Are you sure you want to delete this match group? This will remove all searches and timelines associated to it.',
  ALL_EVENTS = 'All Events',
}
