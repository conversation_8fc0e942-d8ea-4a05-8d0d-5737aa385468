export enum FileDetailDataTestId {
  FILE_ROW_NAME = 'file-row-name',
  VIEW_FILE_BUTTON = 'event-view-file-button',
  DELETE_FILE_BUTTON = 'event-delete-file-button',
  SKELETON = 'skeleton',
  MEDIA_PLAYER = 'media-player',
  TABBED_DETECTIONS = 'tabbed-detections',
  FILE_HEADING_NAME = 'file-heading-name',
  EVENT_BREADCRUMB = 'event-breadcrumb',
  FILE_BREADCRUMB = 'file-breadcrumb',
  PERSON_MATCHED_DETECTION_TAB = 'person-matched-detection-tab',
  TRACKLET = 'Tracklet-',
  FIND_MATCHES_BUTTON = 'file-and-filter-matches__detail-upload-button',
  DETECTED_ATTRIBUTES_CHIP = 'detected-attributes__chip',
  SNACKBAR_BOX = 'snackbar-box-1-1',
  FIND_MATCHES_POPOVER = 'find-matches-popover',
  FIND_MATCHES_POPOVER_CONFIRM = 'find-matches-popover__confirm',
  FIND_MATCHES_POPOVER_CLOSE = 'find-matches-popover__close',
  FIND_MATCHES_POPOVER_NEW_MATCH_CONFIRM = 'find-matches-popover__new-match-confirm',
  FIND_MATCHES_POPOVER_SELECT = 'find-matches-popover-select',
  EVENT_FILES_TAB = 'event-files-tab',
  EVENT_MATCH_GROUPS_TAB = 'event-match-groups-tab',
  EVENT_GENERATED_TIMELINE_VIDEOS_TAB = 'event-generated-timeline-videos-tab',
  MATCH_GROUP_ROW_MENU_ICON = 'match-group-row-menu-icon-',
  THUMBNAIL_SCALER_BUTTON = 'thumbnail-scaler-button',
  THUMBNAIL_SCALER_SLIDER = 'thumbnail-scaler-slider',
  FILE_AND_FILTER_MATCHES_ATTRIBUTES_SELECT = 'file-and-filter-matches-attributes-select',
  TABLE_PAGINATION_PAGE_SIZE = 'table-pagination-page-size',
  TABLE_PAGINATION_PAGE_SIZE_MENU_ITEM = 'table-pagination-page-size-menu-item-',
  TABLE_PAGINATION_PAGE_SELECTOR_NEXT = 'table-pagination-page-selector-next',
  TABLE_PAGINATION_PAGE_SELECTOR_BACK = 'table-pagination-page-selector-back',
}

export enum TabName {
  FILES = 'Files',
  MATCH_GROUPS = 'Match Groups',
  TIMELINE_GENERATED_VIDEOS = 'Timeline Generated Videos',
}

export enum DetailsPanelButton {
  VIEW_FILE = 'View File',
  DELETE_FILE = 'Delete File',
}

export enum PopoverButton {
  CONTINUE = 'Continue',
  CANCEL = 'Cancel',
}

export enum AccordionName {
  DETECTED_ATTRIBUTES = 'Detected Attributes',
  FILE_METADATA = 'File Metadata',
}

export enum VideoControl {
  PLAY = 'Play',
  PAUSE = 'Pause',
}

export enum PageButton {
  NEXT = 'Next',
  PREVIOUS = 'Previous',
}
