import { Before, Then, When, DataTable } from '@badeball/cypress-cucumber-preprocessor';
import { matchGroupPage } from '../../../pages/matchGroupPage';
import { eventDetailsPage } from '../../../pages/eventDetailsPage';
import { mainPage } from '../../../pages/mainPage';
import { MatchGroupButton, MatchGroupTab } from '../../../support/helperFunction/matchGroupHelper';

Before({ tags: '@match-group' }, () => {
  cy.LoginLandingPage();
  cy.intercept('GET', /api\/v1\/events\/\?sortBy/).as('fetchEvent');
  cy.intercept('GET', /api\/v1\/match-groups/).as('fetchMatchGroups');
});

When('The user clicks {string} for the first match group', (action: string) => {
  if (action === MatchGroupButton.VIEW_MATCH_GROUP) {
    cy.get('[data-testid^="match-group-row-"]').first().within(() => {
      cy.contains('button', MatchGroupButton.VIEW_MATCH_GROUP).click();
    });
  }
});

Then('The user should see the match group page UI', () => {
  matchGroupPage.verifyMatchGroupPageUI();
});

Then('The user should see the breadcrumb with event name {string} and group name', (eventName: string) => {
  cy.get('[data-testid="match-group-breadcrumb"]').invoke('text').then((groupName) => {
    matchGroupPage.verifyBreadcrumb(eventName, groupName);
  });
});

Then('The user should see the File Name dropdown and Export button', () => {
  matchGroupPage.verifyFileNameDropdownAndExportButton();
});

Then('The user should see {string} on the left screen', (text: string) => {
  if (text === 'Select a Detection to View Details') {
    matchGroupPage.verifyLeftScreenContent();
  }
});

Then('The user should see the right screen tabs {string}, {string}, and {string}', (_tab1: string, _tab2: string, _tab3: string) => {
  matchGroupPage.verifyRightScreenTabs();
});

Then('The {string} tab should be selected by default', (tabName: string) => {
  if (tabName === MatchGroupTab.VERIFIED_MATCHES) {
    matchGroupPage.verifyDefaultTab();
  }
});

When('The user hovers on a match group search', () => {
  matchGroupPage.hoverOnFirstMatchGroupSearch();
});

When('The user clicks on the edit icon for the search', () => {
  matchGroupPage.clickFirstEditIcon();
});

When('The user enters {string} as the new search name', (newName: string) => {
  matchGroupPage.enterNewSearchName(newName);
});

When('The user clicks {string} in the dialog', (buttonText: string) => {
  if (buttonText === MatchGroupButton.SAVE) {
    matchGroupPage.clickSaveInDialog();
  } else if (buttonText === MatchGroupButton.DELETE) {
    matchGroupPage.clickDeleteInDialog();
  }
});

Then('The search name should be updated to {string}', (newName: string) => {
  matchGroupPage.verifySearchNameUpdated(newName);
});

When('The user clicks on the delete icon for the search', () => {
  matchGroupPage.clickFirstDeleteIcon();
});

Then('The match group search should be deleted successfully', () => {
  cy.get('[data-testid^="match-group-row-"]').should('have.length.lessThan', 2);
});

When('The user clicks on the 3 dots button', () => {
  matchGroupPage.clickThreeDotsButton();
});

When('The user selects {string}', (option: string) => {
  if (option === 'Rename') {
    matchGroupPage.selectRename();
  } else if (option === 'Delete') {
    matchGroupPage.selectDelete();
  }
});

When('The user enters {string} as the new name', (newName: string) => {
  matchGroupPage.enterNewMatchGroupName(newName);
});

Then('The match group name should be updated to {string}', (newName: string) => {
  matchGroupPage.verifyMatchGroupNameUpdated(newName);
});

Then('The user should see the delete confirmation modal', () => {
  matchGroupPage.verifyDeleteConfirmationModal();
});

Then('The confirmation modal should display the message {string}', (_message: string) => {
  matchGroupPage.verifyDeleteConfirmationModal();
});

Then('The modal should have {string} and {string} buttons', (_button1: string, _button2: string) => {
  matchGroupPage.verifyDeleteConfirmationModal();
});

When('The user clicks {string}', (buttonText: string) => {
  if (buttonText === MatchGroupButton.YES_DELETE) {
    matchGroupPage.clickYesDelete();
  }
});

Then('The match group should be deleted successfully', () => {
  matchGroupPage.verifyMatchGroupDeleted();
});

When('The user expands the first match group', () => {
  eventDetailsPage.expandFirstMatchGroup();
});

When('The user hovers on a match group search', () => {
  eventDetailsPage.hoverOnFirstMatchGroupSearch();
});

When('The user clicks on the edit icon for the search', () => {
  eventDetailsPage.clickFirstSearchEditIcon();
});

When('The user enters {string} as the new search name', (newName: string) => {
  eventDetailsPage.enterNewSearchNameInDialog(newName);
});

When('The user clicks {string} in the search dialog', (buttonText: string) => {
  if (buttonText === MatchGroupButton.SAVE) {
    eventDetailsPage.clickSaveInSearchDialog();
  } else if (buttonText === MatchGroupButton.DELETE) {
    eventDetailsPage.clickDeleteInSearchDialog();
  }
});

Then('The search name should be updated to {string}', (newName: string) => {
  eventDetailsPage.verifySearchNameUpdated(newName);
});

When('The user clicks on the delete icon for the search', () => {
  eventDetailsPage.clickFirstSearchDeleteIcon();
});

Then('The match group search should be deleted successfully', () => {
  eventDetailsPage.verifySearchDeleted();
});

When('The user clicks on the 3 dots button for the first match group', () => {
  eventDetailsPage.clickThreeDotsButtonForFirstMatchGroup();
});

When('The user selects {string}', (option: string) => {
  if (option === 'Rename') {
    eventDetailsPage.selectRenameFromMenu();
  } else if (option === 'Delete') {
    eventDetailsPage.selectDeleteFromMenu();
  }
});

When('The user enters {string} as the new name', (newName: string) => {
  eventDetailsPage.enterNewMatchGroupNameInDialog(newName);
});

When('The user clicks {string} in the match group dialog', (buttonText: string) => {
  if (buttonText === MatchGroupButton.SAVE) {
    eventDetailsPage.clickSaveInMatchGroupDialog();
  }
});

Then('The match group name should be updated to {string}', (newName: string) => {
  eventDetailsPage.verifyMatchGroupNameUpdated(newName);
});

Then('The user should see the delete confirmation modal', () => {
  eventDetailsPage.verifyMatchGroupDeleteConfirmationModal();
});

When('The user clicks {string}', (buttonText: string) => {
  if (buttonText === MatchGroupButton.YES_DELETE) {
    eventDetailsPage.clickYesDeleteInMatchGroupDialog();
  }
});

Then('The match group should be deleted successfully', () => {
  eventDetailsPage.verifyMatchGroupDeleted();
});

Then('The user should navigate to match group event details page', () => {
  mainPage.verifyNavigationToEventDetails();
});

When('The user enters {string} into the match group search bar', (keyword: string) => {
  mainPage.enterSearchKeyword(keyword);
});

Then('The displayed match group event results should contain {string}', (keyword: string) => {
  mainPage.verifySearchResults(keyword, 'events');
});

When('The user selects the match group {string} named {string}', (type: 'event' | 'file', eventName: string) => {
  mainPage.selectItem(eventName, type);
});

When('The user clicks on match group {string}', (buttonText: string) => {
  if (buttonText === 'View Event') {
    mainPage.clickViewEventButton();
  }
});

When('The user clicks the match group {string} tab', (tabName: string) => {
  mainPage.clickTab(tabName);
  if (tabName.toLowerCase() === 'files') {
    cy.waitFilesTabIsLoaded();
  } else {
    cy.waitMainPageIsLoaded();
  }
});

Then('The user should see the following match group UI elements:', (dataTable: DataTable) => {
  const rows = dataTable.hashes() as Array<{ 'Element Type': string; 'Expected Value': string }>;

  rows.forEach((row) => {
    const elementType = row['Element Type'];
    const expectedValue = row['Expected Value'];

    switch (elementType) {
    case 'Breadcrumb':
      matchGroupPage.verifyBreadcrumb(expectedValue, '');
      break;
    case 'File Name Dropdown':
      if (expectedValue === 'visible') {
        matchGroupPage.verifyFileNameDropdownAndExportButton();
      }
      break;
    case 'Export Button':
      if (expectedValue === 'visible') {
        matchGroupPage.verifyFileNameDropdownAndExportButton();
      }
      break;
    case 'Left Panel Text':
      if (expectedValue === 'Select a Detection to View Details') {
        matchGroupPage.verifyLeftScreenContent();
      }
      break;
    case 'Tab 1':
    case 'Tab 2':
    case 'Tab 3':
      matchGroupPage.verifyRightScreenTabs();
      break;
    case 'Default Tab':
      if (expectedValue === 'Verified Matches') {
        matchGroupPage.verifyDefaultTab();
      }
      break;
    default:
      throw new Error(`Unknown element type: ${elementType}`);
    }
  });
});
